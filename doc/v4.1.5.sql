alter table supplier.platform_item_inventory_setting
    comment '废弃';
alter table supplier.platform_item_sku_inventory_setting
    comment '废弃';
alter table supplier.shop_inventory
    comment '废弃';
alter table supplier.shop_inventory_goods
    comment '废弃';
alter table supplier.shop_inventory_setting
    comment '废弃';
alter table supplier.virtual_warehouse
    comment '废弃';
alter table supplier.virtual_warehouse_inventory
    comment '废弃';
alter table supplier.virtual_warehouse_inventory_goods
    comment '废弃';
alter table supplier.warehouse_goods_inventory_statics
    comment '废弃';
alter table supplier.warehouse_goods_inventory_lock_statics
    comment '废弃';


CREATE TABLE `inventory_alloc_shop`
(
    `id`               bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`       bigint           DEFAULT 0  NOT NULL COMMENT '创建时间',
    `created_uid`      bigint           DEFAULT 0  NOT NULL COMMENT '创建人',
    `updated_at`       bigint           DEFAULT 0  NOT NULL COMMENT '更新时间',
    `updated_uid`      bigint           DEFAULT 0  NOT NULL COMMENT '更新人',
    `is_del`           bigint           DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`       bigint           DEFAULT 0  NOT NULL COMMENT '删除时间',
    `shop_id`          bigint                      NOT NULL COMMENT '店铺ID',
    `inventory_weight` int              default 0  NOT NULL COMMENT '库存分配权重',
    `status`           tinyint unsigned default 1  NOT NULL COMMENT '状态 1:启用 2:禁用 3:平台无法同步',
    `warn_threshold`   int              DEFAULT -1 NOT NULL COMMENT '低库存预警',
    `next_alloc_time`  bigint           DEFAULT NULL COMMENT '下次库存自动分配时间',
    `alloc_interval`   int              DEFAULT -1 NOT NULL COMMENT '库存分配周期',
    `next_sync_time`   bigint           DEFAULT NULL COMMENT '下次库存同步时间',
    `sync_interval`    int              DEFAULT -1 NOT NULL COMMENT '库存同步周期',
    `last_alloc_time`  bigint           DEFAULT NULL COMMENT '上次库存自动分配时间',
    `last_sync_time`   bigint           DEFAULT NULL COMMENT '上次库存同步时间'
) comment '库存分配店铺设置';

CREATE TABLE `inventory_alloc`
(
    `id`                   bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`           bigint  DEFAULT 0  NOT NULL COMMENT '创建时间',
    `created_uid`          bigint  DEFAULT 0  NOT NULL COMMENT '创建人',
    `updated_at`           bigint  DEFAULT 0  NOT NULL COMMENT '更新时间',
    `updated_uid`          bigint  DEFAULT 0  NOT NULL COMMENT '更新人',
    `is_del`               bigint  DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`           bigint  DEFAULT 0  NOT NULL COMMENT '删除时间',
    `platform_item_id`     bigint             NOT NULL COMMENT '平台商品ID',
    `platform_item_sku_id` bigint             NOT NULL COMMENT '平台商品SkuID',
    `sku_code`             varchar(128)       NOT NULL COMMENT 'SKU编码',
    `total_inventory_num`  int     DEFAULT 0  NOT NULL COMMENT '总库存数',
    `calc_num`             int     DEFAULT -1 NOT NULL COMMENT '系统计算出的分配数量',
    `effective_num`        int     DEFAULT -1 NOT NULL COMMENT '最终生效的分配数量（允许人工调整）',
    `warn_threshold`       int     DEFAULT -1 NOT NULL COMMENT '低库存预警',
    `lock_enabled`         tinyint DEFAULT 0  NOT NULL COMMENT '是否锁定库存',
    `sync_enabled`         tinyint DEFAULT 0  NOT NULL COMMENT '是否开启库存同步',
    `last_alloc_time`      bigint  DEFAULT 0  NOT NULL COMMENT '最后分配时间',
    `last_sync_time`       bigint  DEFAULT 0  NOT NULL COMMENT '最后同步时间',
    `last_sync_num`        int     DEFAULT -1 NOT NULL COMMENT '最后同步库存',
    `is_suite`             tinyint DEFAULT 0  NOT NULL COMMENT '是否是组合装 0：非组合装 1：组合装单品 2：组合装',
    `calculation`          text comment '计算过程'
) comment '库存分配';

alter table platform_item
    change explicitly_sync sync_enabled tinyint default 0 not null comment '是否开启库存同步',
    add `lock_enabled` tinyint DEFAULT 0 NOT NULL COMMENT '是否开启库存同步';


alter table purchase_order
    add column `oa_seal_process_id` varchar(128) default '' not null comment 'OA盖章流程ID';


update  msg_template set content = '${skuTitle}，低于警戒库存，请及时安排进货，并提交采购订单！' where code = 'STOCK_SPEC_ALERT';
