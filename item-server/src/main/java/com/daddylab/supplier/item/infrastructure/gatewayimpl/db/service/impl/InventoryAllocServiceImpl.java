package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryAllocMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 库存分配 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class InventoryAllocServiceImpl
        extends DaddyServiceImpl<InventoryAllocMapper, InventoryAlloc>
        implements IInventoryAllocService {

    @Override
    public List<InventoryAlloc> listByPlatformItemId(Long platformItemId) {
        return lambdaQuery().eq(InventoryAlloc::getPlatformItemId, platformItemId).list();
    }

    @Override
    public List<InventoryAlloc> listBySkuCode(Collection<String> skuCode) {
        return lambdaQuery().eq(InventoryAlloc::getSkuCode, skuCode).list();
    }
}
