package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.date.StopWatch;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig.ItemTypeWeightConfig;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemSkuStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class InventoryAllocBizServiceImpl implements InventoryAllocBizService {
  @Autowired IInventoryAllocService inventoryAllocService;
  @Autowired IInventoryAllocShopService inventoryAllocShopService;
  @Autowired IPlatformItemSkuService platformItemSkuService;
  @Autowired IPlatformItemService platformItemService;
  @Autowired PlatformItemSyncConfig platformItemSyncConfig;
  @Autowired IShopService shopService;
  @Autowired IWdtStockSpecRtService wdtStockSpecRtService;
  @Autowired IInventoryMonitorService inventoryMonitorService;
  @Autowired ICombinationItemService combinationItemService;
  @Autowired IComposeSkuService composeSkuService;

  @Override
  public void allocInventory() {
    StopWatch watch = new StopWatch("库存分配");

    watch.start("查询待分配店铺");
    List<InventoryAllocShop> allocShopList =
        inventoryAllocShopService
            .lambdaQuery()
            .ne(InventoryAllocShop::getStatus, InventoryAllocShopStatus.FORBIDDEN)
            .list();
    watch.stop();
    log.info("[库存分配]待分配店铺，共计={} time={}ms", allocShopList.size(), watch.getLastTaskTimeMillis());

    List<Shop> shops =
        shopService.listByIds(
            allocShopList.stream().map(InventoryAllocShop::getShopId).collect(Collectors.toList()));
    Map<Long, Shop> shopMap =
        shops.stream().collect(Collectors.toMap(Shop::getId, Function.identity()));
    int totalWeight = allocShopList.stream().mapToInt(InventoryAllocShop::getInventoryWeight).sum();
    List<ShopWeight> shopWeights =
        allocShopList.stream()
            .map(
                inventoryAllocShop ->
                    new ShopWeight(
                        shopMap.get(inventoryAllocShop.getShopId()),
                        inventoryAllocShop.getInventoryWeight(),
                        totalWeight))
            .collect(Collectors.toList());
    log.info("[库存分配]店铺权重:{}", shopWeights);

    watch.start("查询所有在售链接商品SKU");
    ListOuterSkuCodeQuery listOuterSkuCodeQuery = new ListOuterSkuCodeQuery();
    listOuterSkuCodeQuery.setPlatformItemSkuStatus(
        Lists.newArrayList(PlatformItemSkuStatus.ON_SALE.getValue()));
    List<String> allOuterSkuCodes = platformItemSkuService.listOuterSkuCode(listOuterSkuCodeQuery);
    watch.stop();
    log.info(
        "[库存分配]查询到所有在售链接商品SKU，去重后共计={}，time={}ms",
        allOuterSkuCodes.size(),
        watch.getLastTaskTimeMillis());

    watch.start("按编码分配库存");
    Flux.fromIterable(allOuterSkuCodes)
        .window(10)
        .concatMap(batch -> batch.flatMap(skuCode -> allocInventory(skuCode, shopWeights)))
        .blockLast();
    watch.stop();
    log.info(
        "[库存分配]库存分配完成，分配耗时={}ms，总耗时={}ms",
        watch.getLastTaskTimeMillis(),
        watch.getTotalTimeMillis());
  }

  private Mono<Void> allocInventory(String skuCode, List<ShopWeight> weights) {
    log.debug("[库存分配]开始分配商品SKU:{}", skuCode);
    Long combinationItemId = combinationItemService.isCombinationItem(skuCode);
    if (combinationItemId > 0) {
      List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItemId);
      if (composeSkus.isEmpty()) {
        log.debug("[库存分配]商品SKU={}，组合装单品列表为空，跳过分配", skuCode);
        return Mono.empty();
      }
      List<PlatformItemSku> directPisList =
          platformItemSkuService
              .lambdaQuery()
              .eq(PlatformItemSku::getOuterSkuCode, skuCode)
              .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
              .list();
      if (directPisList.isEmpty()) {
        log.debug("[库存分配]商品SKU={}，组合装在售的平台商品SKU列表为空，跳过分配", skuCode);
        return Mono.empty();
      }
      List<String> composeSkuCodes =
          composeSkus.stream().map(ComposeSku::getSkuCode).collect(Collectors.toList());
      List<PlatformItemSku> composeSkuPisList = platformItemSkuService
          .lambdaQuery()
          .in(PlatformItemSku::getOuterSkuCode, composeSkuCodes)
          .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
          .list();
      List<Long> composeSkuPisIds =
          composeSkuPisList.stream().map(PlatformItemSku::getPlatformItemId).collect(Collectors.toList());
      List<PlatformItem> composeSkuPiList =
          platformItemService.listByIds(composeSkuPisIds);
      Map<Long, PlatformItem> composeSkuPiMap =
          composeSkuPiList.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
      List<Long> directPlatformItemIds =
          directPisList.stream().map(PlatformItemSku::getPlatformItemId).collect(Collectors.toList());
      List<PlatformItem> directPlatformItems =
          platformItemService.listByIds(directPlatformItemIds);
      Map<Long, PlatformItem> directPlatformItemMap =
          directPlatformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity())); 
      List<Long> allPlatformItemIds = new ArrayList<Long>(directPlatformItemIds);
      allPlatformItemIds.addAll(composeSkuPisIds);
      List<PlatformItem> allPlatformItems =
          platformItemService.listByIds(allPlatformItemIds);
      Map<Long, PlatformItem> allPlatformItemMap =
          allPlatformItems.stream().collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
      List<WdtStockSpecRt> stockSpecRts =
          wdtStockSpecRtService
            .lambdaQuery()
            .in(WdtStockSpecRt::getSpecNo, composeSkuCodes)
            .eq(WdtStockSpecRt::getDefect, 0)
           .list();
      // 按SKU编码分组计算可用库存总和
      Map<String, BigDecimal> skuAvailableStockMap = stockSpecRts.stream()
          .collect(Collectors.groupingBy(
              WdtStockSpecRt::getSpecNo,
              Collectors.reducing(
                  BigDecimal.ZERO,
                  WdtStockSpecRt::getAvailableStock,
                  BigDecimal::add)
          ));
      
      // 查询警戒库存
      List<InventoryMonitor> inventoryMonitors =
          inventoryMonitorService
            .lambdaQuery()
            .in(InventoryMonitor::getSkuNo, composeSkuCodes)
            .eq(InventoryMonitor::getStatus, 1)
            .eq(InventoryMonitor::getThresholdType, 1)
            .list();

      // 按SKU编码分组计算警戒库存总和
      Map<String, Integer> skuAlertThresholdMap = inventoryMonitors.stream()
          .collect(Collectors.groupingBy(
              InventoryMonitor::getSkuNo,
              Collectors.summingInt(InventoryMonitor::getAlertThreshold)
          ));

      List<InventoryAlloc> inventoryAllocs = new ArrayList<InventoryAlloc>();
      for (ShopWeight shopWeight : weights) {
        Shop shop = shopWeight.getShop();
        String shopNo = shop.getSn();
        // 计算组合装可用库存
        BigDecimal minCombinationStock = null;
        for (ComposeSku composeSku : composeSkus) {
            String composeSkuCode = composeSku.getSkuCode();
            BigDecimal skuAvailableStock = skuAvailableStockMap.getOrDefault(composeSkuCode, BigDecimal.ZERO);
            Integer alertThreshold = skuAlertThresholdMap.getOrDefault(composeSkuCode, 0);
            
            // 实际可用库存 = 可用库存 - 警戒库存
            BigDecimal actualAvailableStock = skuAvailableStock.subtract(new BigDecimal(alertThreshold));
            if (actualAvailableStock.compareTo(BigDecimal.ZERO) <= 0) {
                minCombinationStock = BigDecimal.ZERO;
                break;
            }
            
            // 计算单品可组成的最大套装数量
            BigDecimal maxCombinations = actualAvailableStock.divide(
                new BigDecimal(composeSku.getCount()), 
                0, 
                RoundingMode.DOWN
            );
            
            // 取最小值作为实际可组装数量
            if (minCombinationStock == null || maxCombinations.compareTo(minCombinationStock) < 0) {
                minCombinationStock = maxCombinations;
            }
        }

        if (minCombinationStock == null || minCombinationStock.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("[库存分配]组合装SKU={}，店铺{}，可用库存为0，跳过分配", skuCode, shopNo);
            continue;
        }

        // 获取该店铺下的组合装商品SKU
        List<PlatformItemSku> shopDirectPisList = directPisList.stream()
            .filter(sku -> sku.getShopNo().equals(shopNo))
            .collect(Collectors.toList());

        if (shopDirectPisList.isEmpty()) {
            log.debug("[库存分配]组合装SKU={}，店铺{}，未找到关联的平台商品SKU，跳过分配", skuCode, shopNo);
            continue;
        }

        // 平均分配到每个SKU
        BigDecimal avgStock = minCombinationStock.divide(
            new BigDecimal(shopDirectPisList.size()),
            0,
            RoundingMode.HALF_UP
        );

        // 计算平均分配后的精度误差
        BigDecimal correctValue = minCombinationStock.subtract(
            avgStock.multiply(new BigDecimal(shopDirectPisList.size()))
        );

        if (shop.getPlatform() == Platform.LAOBASHOP) {
            // 计算总权重
            int totalShopSkuWeight = 0;
            for (PlatformItemSku sku : shopDirectPisList) {
                PlatformItem platformItem = allPlatformItemMap.get(sku.getPlatformItemId());
                if (platformItem == null) {
                    continue;
                }
                ItemTypeWeightConfig itemTypeWeightConfig =
                        platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
                totalShopSkuWeight += itemTypeWeightConfig.getWeight();
            }

            BigDecimal remainingShopStock = minCombinationStock;
            // 分配组合装商品SKU库存
            for (int i = 0; i < shopDirectPisList.size(); i++) {
                PlatformItemSku platformItemSku = shopDirectPisList.get(i);
                PlatformItem platformItem = allPlatformItemMap.get(platformItemSku.getPlatformItemId());
                if (platformItem == null) {
                    log.warn("[库存分配]组合装SKU={}，店铺{}，平台商品SKU ID={}，未找到关联的平台商品，跳过分配",
                            skuCode, shopNo, platformItemSku.getId());
                    continue;
                }
                ItemTypeWeightConfig itemTypeWeightConfig =
                        platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
                BigDecimal allocStock;
                String calculation;
                if (i == shopDirectPisList.size() - 1) {
                    allocStock = remainingShopStock;
                    calculation = String.format("剩余库存. 总库存=%s", minCombinationStock);
                } else {
                    BigDecimal weightPercent = new BigDecimal(itemTypeWeightConfig.getWeight())
                            .divide(new BigDecimal(totalShopSkuWeight), 4, RoundingMode.HALF_UP);
                    allocStock = minCombinationStock.multiply(weightPercent).setScale(0, RoundingMode.HALF_UP);
                    remainingShopStock = remainingShopStock.subtract(allocStock);
                    calculation =
                        String.format(
                            "总库存 * (商品类型权重/店铺总权重) = %s * (%s/%s) = %s",
                            minCombinationStock,
                            itemTypeWeightConfig.getWeight(),
                            totalShopSkuWeight,
                            allocStock.intValue());
                }

                InventoryAlloc inventoryAlloc = new InventoryAlloc();
                inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
                inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
                inventoryAlloc.setSkuCode(platformItemSku.getSkuCode());
                inventoryAlloc.setTotalInventoryNum(minCombinationStock.intValue());
                inventoryAlloc.setCalcNum(allocStock.intValue());
                inventoryAlloc.setEffectiveNum(allocStock.intValue());
                inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
                inventoryAlloc.setCalculation(calculation);
                inventoryAllocs.add(inventoryAlloc);
            }
        } else {
            // 其他平台保持平均分配
            for (int i = 0; i < shopDirectPisList.size(); i++) {
                PlatformItemSku platformItemSku = shopDirectPisList.get(i);
                BigDecimal allocStock = avgStock;
                if (i == shopDirectPisList.size() - 1) {
                    allocStock = allocStock.add(correctValue);
                }
                String calculation =
                    String.format(
                        "总库存 / 店铺SKU数 = %s / %s = %s",
                        minCombinationStock, shopDirectPisList.size(), allocStock.intValue());
                InventoryAlloc inventoryAlloc = new InventoryAlloc();
                inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
                inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
                inventoryAlloc.setSkuCode(platformItemSku.getSkuCode());
                inventoryAlloc.setTotalInventoryNum(minCombinationStock.intValue());
                inventoryAlloc.setCalcNum(allocStock.intValue());
                inventoryAlloc.setEffectiveNum(allocStock.intValue());
                inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
                inventoryAlloc.setCalculation(calculation);
                inventoryAllocs.add(inventoryAlloc);
            }
        }
      }
    } else {
      List<PlatformItemSku> directPlatformItemSkus =
          platformItemSkuService
              .lambdaQuery()
              .eq(PlatformItemSku::getOuterSkuCode, skuCode)
              .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
              .list();
      if (directPlatformItemSkus.isEmpty()) {
        log.debug("[库存分配]商品SKU={}，未找到关联的在售平台商品SKU，跳过分配", skuCode);
        return Mono.empty();
      }
      List<Long> directPlatformItemIds =
          directPlatformItemSkus.stream()
              .map(PlatformItemSku::getPlatformItemId)
              .collect(Collectors.toList());
      List<PlatformItem> directPlatformItems = platformItemService.listByIds(directPlatformItemIds);
      ArrayList<PlatformItem> allPlatformItems = new ArrayList<PlatformItem>(directPlatformItems);
      List<PlatformItemSku> allPlatformItemSkus =
          new ArrayList<PlatformItemSku>(directPlatformItemSkus);
      // 查询套装内包含此单品的组合装
      List<CombinationItem> relatedCombinationItems = combinationItemService.listBySkuCode(skuCode);
      // 查询组合装在售的平台商品SKU
      List<PlatformItemSku> relatedCombinationPisList = Collections.emptyList();
      if (!relatedCombinationItems.isEmpty()) {
        List<String> relatedCombinationCodes =
            relatedCombinationItems.stream()
                .map(CombinationItem::getCode)
                .collect(Collectors.toList());
        relatedCombinationPisList =
            platformItemSkuService
                .lambdaQuery()
                .in(PlatformItemSku::getOuterSkuCode, relatedCombinationCodes)
                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue())
                .list();
        List<Long> relatedCombinationPisIds =
            relatedCombinationPisList.stream()
                .map(PlatformItemSku::getCombinationItemId)
                .collect(Collectors.toList());
        List<PlatformItem> relatedCombinationPiList =
            platformItemService.listByIds(relatedCombinationPisIds);
        allPlatformItems.addAll(relatedCombinationPiList);
        allPlatformItemSkus.addAll(relatedCombinationPisList);
      }
      Map<Long, PlatformItem> allPlatformItemMap =
          allPlatformItems.stream()
              .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
      List<WdtStockSpecRt> stockSpecRts =
          wdtStockSpecRtService
              .lambdaQuery()
              .eq(WdtStockSpecRt::getSpecNo, skuCode)
              .eq(WdtStockSpecRt::getDefect, 0)
              .list();
      BigDecimal availableStockSum =
          stockSpecRts.stream()
              .map(WdtStockSpecRt::getAvailableStock)
              .reduce(BigDecimal.ZERO, BigDecimal::add);
      // 查询警戒库存
      List<InventoryMonitor> inventoryMonitors =
          inventoryMonitorService
              .lambdaQuery()
              .eq(InventoryMonitor::getSkuNo, skuCode)
              .eq(InventoryMonitor::getStatus, 1)
              .eq(InventoryMonitor::getThresholdType, 1)
              .list();
      // 警戒库存总和
      int stockAlertThresholdSum =
          inventoryMonitors.stream().mapToInt(InventoryMonitor::getAlertThreshold).sum();
      BigDecimal stockTotal = availableStockSum;
      if (stockAlertThresholdSum > 0) {
        stockTotal = availableStockSum.subtract(new BigDecimal(stockAlertThresholdSum));
      }
      List<Long> combinationPlatformItemSkuIds =
          relatedCombinationPisList.stream()
              .map(PlatformItemSku::getId)
              .collect(Collectors.toList());
      log.debug(
          "[库存分配]商品SKU={}，总库存={}，可用库存={}，警戒库存={}，在售平台商品SKU={}，关联组合装在售的平台商品SKU={}",
          skuCode,
          stockTotal,
          availableStockSum,
          stockAlertThresholdSum,
          directPlatformItemIds,
          combinationPlatformItemSkuIds);
      if (stockTotal.compareTo(BigDecimal.ZERO) == 0) {
        log.debug("[库存分配]商品SKU={}，总库存为0，跳过分配", skuCode);
        return Mono.empty();
      }
      BigDecimal remainStock = availableStockSum;
      for (int i = 0; i < weights.size(); i++) {
        ShopWeight shopWeight = weights.get(i);
        Shop shop = shopWeight.getShop();
        String shopNo = shop.getSn();
        BigDecimal shopStock;
        if (i == weights.size() - 1) {
          shopStock = remainStock;
        } else {
          shopStock =
              shopWeight.getPercent().multiply(stockTotal).setScale(0, RoundingMode.HALF_UP);
          remainStock = remainStock.subtract(shopStock);
        }
        log.debug("[库存分配]商品SKU={}，店铺{}，分配库存={}", skuCode, shopNo, shopStock);
        if (shopStock.compareTo(BigDecimal.ZERO) == 0) {
          log.debug("[库存分配]商品SKU={}，店铺{}，分配库存为0，跳过分配", skuCode, shopNo);
          continue;
        }
        List<PlatformItemSku> shopDirectPisList =
            directPlatformItemSkus.stream()
                .filter(sku -> sku.getShopNo().equals(shopNo))
                .collect(Collectors.toList());
        if (shopDirectPisList.isEmpty()) {
          log.debug("[库存分配]商品SKU={}，店铺{}，未找到关联的平台商品SKU，跳过分配", skuCode, shopNo);
          continue;
        }
        List<PlatformItemSku> shopCombinationSkuList =
            relatedCombinationPisList.stream()
                .filter(sku -> sku.getShopNo().equals(shopNo))
                .collect(Collectors.toList());
        if (shop.getPlatform() == Platform.LAOBASHOP) {
          // 计算总权重
          int totalShopSkuWeight = 0;
          for (PlatformItemSku sku : allPlatformItemSkus) {
            PlatformItem platformItem = allPlatformItemMap.get(sku.getPlatformItemId());
            if (platformItem == null) {
              log.warn(
                  "[库存分配]商品SKU={}，店铺{}，平台商品SKU ID={}，未找到关联的平台商品，跳过分配",
                  skuCode,
                  shopNo,
                  sku.getId());
              continue;
            }
            ItemTypeWeightConfig itemTypeWeightConfig =
                platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            totalShopSkuWeight += itemTypeWeightConfig.getWeight();
          }

          BigDecimal remainingShopStock = shopStock;
          ArrayList<InventoryAlloc> inventoryAllocs = new ArrayList<>();

          // 分配普通商品SKU库存
          for (int i1 = 0; i1 < shopDirectPisList.size(); i1++) {
            PlatformItemSku platformItemSku = shopDirectPisList.get(i1);
            PlatformItem platformItem = allPlatformItemMap.get(platformItemSku.getPlatformItemId());
            if (platformItem == null) {
              log.warn(
                  "[库存分配]商品SKU={}，店铺{}，平台商品SKU ID={}，未找到关联的平台商品，跳过分配",
                  skuCode,
                  shopNo,
                  platformItemSku.getId());
              continue;
            }
            PlatformItemSyncConfig.ItemTypeWeightConfig itemTypeWeightConfig =
                platformItemSyncConfig.getItemTypeWeightConfig(platformItem.getGoodsName());
            BigDecimal allocStock;
            // 记录计算过程
            StringBuilder calculationProcess = new StringBuilder();
            calculationProcess
                .append("SKU=")
                .append(skuCode)
                .append(", 总可用库存=")
                .append(availableStockSum)
                .append(", 警戒库存=")
                .append(stockAlertThresholdSum)
                .append(", 实际可分配总库存=")
                .append(stockTotal)
                .append("\n店铺=")
                .append(shopNo)
                .append(", 店铺权重=")
                .append(shopWeight.getWeight())
                .append("/")
                .append(shopWeight.getTotalWeight())
                .append("=")
                .append(shopWeight.getPercent())
                .append("%")
                .append(", 店铺分配库存=")
                .append(shopStock);
            if (i1 == shopDirectPisList.size() - 1) {
              allocStock = remainingShopStock;
              calculationProcess
                  .append("计算方式: 分配剩余库存")
                  .append("\n剩余库存=")
                  .append(remainingShopStock);
            } else {
              // 先将权重转化为百分比
              BigDecimal weightPercent =
                  new BigDecimal(itemTypeWeightConfig.getWeight())
                      .divide(new BigDecimal(totalShopSkuWeight), 6, RoundingMode.HALF_UP);
              allocStock = shopStock.multiply(weightPercent).setScale(0, RoundingMode.HALF_UP);
              remainingShopStock = remainingShopStock.subtract(allocStock);
              calculationProcess
                  .append("计算方式: 分配权重占比")
                  .append("\n权重占比=")
                  .append(itemTypeWeightConfig.getWeight())
                  .append("/")
                  .append(totalShopSkuWeight)
                  .append("=")
                  .append(weightPercent)
                  .append("%")
                  .append(", 分配库存=")
                  .append(allocStock)
                  .append(", 剩余库存=")
                  .append(remainingShopStock);
            }

            Long pisId = platformItemSku.getId();
            log.debug("[库存分配]商品SKU={}，店铺{}，平台商品SKU={}，分配库存={}", skuCode, shopNo, pisId, allocStock);
            InventoryAlloc inventoryAlloc = new InventoryAlloc();
            inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
            inventoryAlloc.setPlatformItemSkuId(pisId);
            inventoryAlloc.setSkuCode(platformItemSku.getSkuCode());
            inventoryAlloc.setTotalInventoryNum(shopStock.intValue());
            inventoryAlloc.setCalcNum(allocStock.intValue());
            inventoryAlloc.setEffectiveNum(allocStock.intValue());
            inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
            inventoryAlloc.setCalculation(calculationProcess.toString());
            inventoryAllocs.add(inventoryAlloc);
          }

          // 保存分配结果
          if (!inventoryAllocs.isEmpty()) {
            inventoryAllocService.saveBatch(inventoryAllocs);
          }
        } else {
          // 非小程序的其他平台，平均分配到每个SKU
          BigDecimal avgStock =
              shopStock.divide(
                  new BigDecimal(shopDirectPisList.size() + shopCombinationSkuList.size()),
                  0,
                  RoundingMode.HALF_UP);
          // 计算平均分配后的精度误差
          List<PlatformItemSku> allShopSkus = new ArrayList<>();
          allShopSkus.addAll(shopDirectPisList);
          allShopSkus.addAll(shopCombinationSkuList);

          BigDecimal correctValue =
              shopStock.subtract(avgStock.multiply(new BigDecimal(allShopSkus.size())));
          ArrayList<InventoryAlloc> inventoryAllocs = new ArrayList<>();
          for (int i1 = 0; i1 < allShopSkus.size(); i1++) {
            PlatformItemSku platformItemSku = allShopSkus.get(i1);
            BigDecimal allocStock = avgStock;
            if (i1 == allShopSkus.size() - 1) {
              allocStock = allocStock.add(correctValue);
            }
            Long pisId = platformItemSku.getId();
            log.debug("[库存分配]商品SKU={}，店铺{}，平台商品SKU={}，分配库存={}", skuCode, shopNo, pisId, allocStock);
            InventoryAlloc inventoryAlloc = new InventoryAlloc();
            inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
            inventoryAlloc.setPlatformItemSkuId(pisId);
            inventoryAlloc.setSkuCode(platformItemSku.getSkuCode());
            inventoryAlloc.setTotalInventoryNum(shopStock.intValue());
            inventoryAlloc.setCalcNum(allocStock.intValue());
            inventoryAlloc.setEffectiveNum(allocStock.intValue());
            inventoryAlloc.setLastAllocTime(DateUtil.currentTime());

            // 记录计算过程
            StringBuilder calculationProcess = new StringBuilder();
            calculationProcess
                .append("SKU=")
                .append(skuCode)
                .append(", 总可用库存=")
                .append(availableStockSum)
                .append(", 警戒库存=")
                .append(stockAlertThresholdSum)
                .append(", 实际可分配库存=")
                .append(stockTotal)
                .append("\n店铺=")
                .append(shopNo)
                .append(", 店铺权重=")
                .append(shopWeight.getWeight())
                .append("/")
                .append(shopWeight.getTotalWeight())
                .append("=")
                .append(shopWeight.getPercent())
                .append("%")
                .append(", 店铺分配库存=")
                .append(shopStock);

            // 非LAOBASHOP平台，记录平均分配的计算逻辑
            calculationProcess
                .append("\n计算方式: 平均分配")
                .append("\n计算公式: 店铺库存 / SKU数量 = ")
                .append(shopStock)
                .append(" / ")
                .append(allShopSkus.size())
                .append(" = ")
                .append(avgStock);

            // 如果是最后一个SKU，记录精度修正
            if (i1 == allShopSkus.size() - 1 && correctValue.compareTo(BigDecimal.ZERO) != 0) {
              calculationProcess
                  .append("\n精度修正: ")
                  .append(avgStock)
                  .append(" + ")
                  .append(correctValue)
                  .append(" = ")
                  .append(allocStock);
            }

            inventoryAlloc.setCalculation(calculationProcess.toString());
            inventoryAllocs.add(inventoryAlloc);
          }
          // 保存分配结果
          if (!inventoryAllocs.isEmpty()) {
            inventoryAllocService.saveBatch(inventoryAllocs);
          }
        }
      }
    }
    return Mono.empty();
  }

  public static class ShopWeight {
    @Getter Shop shop;
    @Getter long weight;
    @Getter long totalWeight;
    @Getter BigDecimal percent;

    public ShopWeight(Shop shop, long weight, long totalWeight) {
      this.shop = shop;
      this.weight = weight;
      this.totalWeight = totalWeight;
      this.percent =
          new BigDecimal(String.valueOf((float) weight / totalWeight * 100))
              .setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public String toString() {
      return String.format(
          "%s:%s/%s=%s(%%)", shop.getSn(), weight, totalWeight, percent.toPlainString());
    }
  }
}
