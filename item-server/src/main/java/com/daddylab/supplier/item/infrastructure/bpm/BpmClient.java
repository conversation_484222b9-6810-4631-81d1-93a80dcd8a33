package com.daddylab.supplier.item.infrastructure.bpm;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.BpmFeignClient;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> up
 * @date 2025年06月18日 5:44 PM
 */
@Component
@Slf4j
@RefreshScope
public class BpmClient {

  @Getter
  @Value("${bpm.oaUser}")
  private String oaUser;

  @Getter
  @Value("${bpm.oaPwd}")
  private String oaPwd;

  @Getter
  @Value("${bpm.contractType2:-7144556016924735554}")
  private String contractType2;

  @Getter
  @Value("${bpm.contractType3:-6019339138934161263}")
  private String contractType3;

  @Autowired BpmFeignClient bpmFeignClient;

  public String getUserOaToken(String userLoginName) {
    return bpmFeignClient.token(oaUser, oaPwd, userLoginName);
  }
}
