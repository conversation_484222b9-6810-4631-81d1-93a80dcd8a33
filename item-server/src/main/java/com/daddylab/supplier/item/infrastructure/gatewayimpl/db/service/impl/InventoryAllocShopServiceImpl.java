package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.InventoryAllocShopMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocShopService;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 库存分配店铺设置 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Service
public class InventoryAllocShopServiceImpl extends DaddyServiceImpl<InventoryAllocShopMapper, InventoryAllocShop> implements IInventoryAllocShopService {

}
