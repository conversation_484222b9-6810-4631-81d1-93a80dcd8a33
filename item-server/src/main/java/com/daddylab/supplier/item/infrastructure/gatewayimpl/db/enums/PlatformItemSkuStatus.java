package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 平台商品SKU状态
 *
 * <p>1:在售 0:已下架
 *
 * <AUTHOR>
 * @version 1.0
 */
@Getter
@AllArgsConstructor
public enum PlatformItemSkuStatus implements IEnum<Integer> {
    /** 已下架 */
    OFF_SALE(0, "已下架"),
    /** 在售 */
    ON_SALE(1, "在售"),
    ;

    @EnumValue private final Integer value;
    private final String desc;
}
