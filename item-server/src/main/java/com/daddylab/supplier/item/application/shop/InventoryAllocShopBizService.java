package com.daddylab.supplier.item.application.shop;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopCmd;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopQuery;
import com.daddylab.supplier.item.application.shop.dto.InventoryAllocShopVo;
import com.daddylab.supplier.item.application.shop.dto.ShopExecutionSignal;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;

import java.util.List;

public interface InventoryAllocShopBizService {

    PageResponse<InventoryAllocShopVo> page(InventoryAllocShopQuery query);

    boolean delete(Long id);

    void saveOrUpdate(InventoryAllocShopCmd cmd);

    void nextHandler(ShopExecutionSignal signal);


    void allocHandler(List<InventoryAllocShop> list);

    void syncHandler(List<InventoryAllocShop> list);
}
