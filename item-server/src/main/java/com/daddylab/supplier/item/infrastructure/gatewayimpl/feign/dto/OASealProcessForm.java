package com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2025年06月19日 1:49 PM
 */
@NoArgsConstructor
@Data
public class OASealProcessForm {

  /** appName */
  String appName = "collaboration";

  /** data */
  @JsonProperty("data")
  DataForm data;

  /** DataDTO */
  @NoArgsConstructor
  @Data
  public static class DataForm {
    /** data */
    @JsonProperty("data")
    DataForm.MainData data;

    /** templateCode */
    String templateCode = "P_HT02";

    /** draft */
    String draft = "0";

    /** relateDoc */
    String relateDoc = "";

    /** subject */
    String subject = "";

    /** DataDTO */
    @NoArgsConstructor
    @Data
    public static class MainData {
      /** formmain0669 */
      @JsonProperty("formmain_0669")
      DataForm.MainData.MainForm mainForm;

      /** formson0670 */
      @JsonProperty("formson_0670")
      List<DataForm.MainData.SubForm> subForms;

      /** thirdAttachments */
      @JsonProperty("thirdAttachments")
      List<DataForm.MainData.ThirdAttachments> thirdAttachments;

      /** Formmain0669DTO */
      @NoArgsConstructor
      @Data
      public static class MainForm {
        /** 发起类型 */
        @JsonProperty("发起类型")
        String startType = "E";

        /** 合同类型1 */
        @JsonProperty("合同类型1")
        String concatType1 = "-3951052332005439382";

        /** 合同类型2。环境不一样 */
        @JsonProperty("合同类型2")
        String concatType2 = "-7144556016924735554";

        /** 合同类型3。环境不一样 */
        @JsonProperty("合同类型3")
        String concatType3 = "-6019339138934161263";

        /** 外部系统主键id */
        @JsonProperty("外部系统主键id")
        String outSystemId;

        /** 对方名称 */
        @JsonProperty("对方名称")
        String bizName;

        /** 是否触发 */
        @JsonProperty("是否触发")
        Integer isActive = 1;

        /** 申请人,staff表的oaid */
        @JsonProperty("申请人")
        String operateId;

        /**
         * 签订公司名称 ：1247864386392245833杭州老爸评测科技股份有限公司, -6050678774602315586杭州老爸电商科技有限公司,
         * 7647788303645230425::杭州老爸评测电子商务有限公司, -4212392206471508587上海雪时晴信息科技有限公司,
         */
        @JsonProperty("签订公司名称")
        String companyName = "";

        /** 隐藏是否第一环节 */
        @JsonProperty("隐藏是否第一环节")
        Integer hideFirst = 0;
      }

      /** Formson0670DTO */
      @NoArgsConstructor
      @Data
      public static class SubForm {
        /** 合同开始日期 yyyy-MM-dd */
        @JsonProperty("合同开始日期")
        String startTime;

        /** 合同描述 */
        @JsonProperty("合同描述")
        private String desc;

        /** 合同结束日期 */
        @JsonProperty("合同结束日期")
        private String endTime;

        /** 合同编号 */
        @JsonProperty("合同编码")
        private String no;

        /** 合同订单金额 */
        @JsonProperty("合同订单金额")
        private Integer amount = 0;

        /** 合同附件 */
        //        @JsonProperty("合同附件")
        //        private Integer attachment = 1;

        /** 审核初稿 */
        @JsonProperty("审核初稿")
        private String firstDraft = "1";

        /** 是否有框架合同 4114197070147374030是，1986601884381300703否 */
        @JsonProperty("是否有框架合同")
        private Long frameworkContract = 1986601884381300703L;
      }

      /** ThirdAttachmentsDTO */
      @NoArgsConstructor
      @Data
      public static class ThirdAttachments {
        /** subReference */
        String subReference = "1";

        /** fileUrl */
        String fileUrl;

        /** sort */
        Integer sort = 1;
      }
    }
  }
}
