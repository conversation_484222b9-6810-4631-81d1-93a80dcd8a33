package com.daddylab.supplier.item.application.platformItem.data;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import lombok.Data;

@Data
public class InventoryOccupyItemVO {
    private Long inventoryAllocId;
    private Long platformItemId;
    private Long platformItemSkuId;
    private String outerItemId;
    private String outerSkuId;
    private String platformItemName;
    private Platform platform;
    private String skuCode;
    private Integer syncStock;
    private Integer totalStock;
}
