package com.daddylab.supplier.item.application.shop;

import static com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget.INVENTORY_ALLOC_SHOP;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.shop.dto.*;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Shop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IInventoryAllocShopService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IShopService;
import com.daddylab.supplier.item.infrastructure.rocketmq.RocketMQProducer;
import com.daddylab.supplier.item.infrastructure.rocketmq.delay.DelayMsgBizDto;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.ValueChange;
import org.javers.core.metamodel.annotation.Id;
import org.javers.core.metamodel.annotation.PropertyName;
import org.redisson.api.RLock;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

/**
 * <AUTHOR> up
 * @date 2025年06月23日 3:16 PM
 * @version 2.0 (Refactored)
 */
@Slf4j
@Service
public class InventoryAllocShopBizServiceImpl implements InventoryAllocShopBizService {

  @Autowired IInventoryAllocShopService inventoryAllocShopService;
  @Autowired IShopService shopService;
  @Autowired OperateLogGateway operateLogGateway;
  @Autowired RocketMQProducer rocketMQProducer;
  @Autowired RedissonClient redissonClient;

  @Value("${spring.profiles.active}_${spring.application.name}_shop_stock")
  private String shopStockTopic;

  @Value("${rocketmq.delayTopic}")
  String delayTopic;

  // 锁的key保持不变，但其使用范围得到了优化
  private static final String EDITE_KEY = "shop_stock_edite";

  /** 分页查询。 */
  @Override
  public PageResponse<InventoryAllocShopVo> page(InventoryAllocShopQuery query) {
    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(EDITE_KEY);
    RLock readLock = readWriteLock.readLock();
    readLock.lock();
    try {
      final PageInfo<InventoryAllocShop> objectPageInfo =
          PageHelper.startPage(query.getPageIndex(), query.getPageSize())
              .doSelectPageInfo(
                  () ->
                      inventoryAllocShopService
                          .lambdaQuery()
                          .eq(InventoryAllocShop::getShopId, query.getShopId())
                          .orderByDesc(InventoryAllocShop::getId)
                          .list());
      if (objectPageInfo.getTotal() == 0) {
        return PageResponse.of(new ArrayList<>(), 0, query.getPageSize(), query.getPageIndex());
      }

      final List<Long> shopIdList =
          objectPageInfo.getList().stream()
              .map(InventoryAllocShop::getShopId)
              .collect(Collectors.toList());
      final Map<Long, Shop> shopMap =
          shopService.lambdaQuery().in(Shop::getId, shopIdList).list().stream()
              .collect(Collectors.toMap(Shop::getId, Function.identity()));

      final List<InventoryAllocShopVo> voList =
          objectPageInfo.getList().stream()
              .map(
                  val -> {
                    InventoryAllocShopVo vo = new InventoryAllocShopVo();
                    vo.setId(val.getId());
                    vo.setShopId(val.getShopId());
                    final Shop shop = shopMap.get(val.getShopId());
                    vo.setShopName(Objects.nonNull(shop) ? shop.getName() : "");
                    vo.setPlatform(Objects.nonNull(shop) ? shop.getPlatform() : null);
                    vo.setInventoryWeight(val.getInventoryWeight());
                    vo.setStatus(val.getStatus());
                    vo.setWarnThreshold(val.getWarnThreshold());
                    vo.setNextAllocTime(val.getNextAllocTime());
                    vo.setLastAllocTime(val.getLastAllocTime());
                    vo.setAllocInterval(val.getAllocInterval());
                    vo.setNextSyncTime(val.getNextSyncTime());
                    vo.setLastSyncTime(val.getLastSyncTime());
                    vo.setSyncInterval(val.getSyncInterval());
                    return vo;
                  })
              .collect(Collectors.toList());
      return PageResponse.of(
          voList, (int) objectPageInfo.getTotal(), query.getPageSize(), query.getPageIndex());
    } finally {
      readLock.unlock();
    }
  }

  /** 删除店铺。 */
  @Override
  @Transactional
  public boolean delete(Long id) {
    try {
      final InventoryAllocShop inventoryAllocShop = inventoryAllocShopService.getById(id);
      Assert.notNull(inventoryAllocShop, "id非法");
      final Shop shop = shopService.getById(inventoryAllocShop.getShopId());
      Assert.notNull(shop, "店铺id非法");
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          INVENTORY_ALLOC_SHOP,
          0L,
          StrUtil.format("删除同步店铺，店铺：{}", shop.getName()),
          null);
      return inventoryAllocShopService.removeById(id);
    } catch (Exception e) {
      log.error("库存同步，店铺删除异常, id: {}", id, e);
      throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "删除失败");
    }
  }

  @Transactional
  public void saveOrUpdate(InventoryAllocShopCmd cmd) {
    RReadWriteLock readWriteLock = redissonClient.getReadWriteLock(EDITE_KEY);
    RLock writeLock = readWriteLock.writeLock();
    writeLock.lock();
    try {
      saveOrUpdate0(cmd);
    } catch (Exception e) {
      log.error("库存同步，店铺编辑异常", e);
      throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "保存失败");
    } finally {
      writeLock.unlock();
    }
  }

  // 内部实现，被事务和锁包裹
  private void saveOrUpdate0(InventoryAllocShopCmd cmd) {
    boolean isFirstAdd = false;
    boolean allocUpdate = false;
    boolean syncUpdate = false;

    // 查询所有数据用于后续操作，避免重复查询
    final List<InventoryAllocShop> allShops = inventoryAllocShopService.lambdaQuery().list();
    final Map<Long, InventoryAllocShop> existingShopMap =
        allShops.stream().collect(Collectors.toMap(InventoryAllocShop::getId, Function.identity()));

    if (CollUtil.isEmpty(allShops)) {
      isFirstAdd = true;
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          INVENTORY_ALLOC_SHOP,
          0L,
          StrUtil.format(
              "首次新增库存同步配置，下次分配时间：{}，分配周期：{}h，下次同步时间：{}，同步周期：{}h",
              cmd.getNextAllocTime(),
              cmd.getAllocInterval(),
              cmd.getNextSyncTime(),
              cmd.getSyncInterval()),
          null);
    } else {
      final InventoryAllocShop firstShop = allShops.get(0);
      CompareSettingBo oOne = CompareSettingBo.fromEntity(firstShop);
      CompareSettingBo nOne = CompareSettingBo.fromCmd(cmd);

      final Diff diff = DiffUtil.diff(oOne, nOne);
      if (!diff.getChanges().isEmpty()) {
        allocUpdate =
            diff.getChanges().stream()
                .anyMatch(cc -> ((ValueChange) cc).getPropertyName().equals("下次库存自动分配时间"));
        syncUpdate =
            diff.getChanges().stream()
                .anyMatch(cc -> ((ValueChange) cc).getPropertyName().equals("下次库存同步时间"));
        final String simpleDiffLog = DiffUtil.getSimpleDiffLog(diff, CompareSettingBo.class);
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            INVENTORY_ALLOC_SHOP,
            0L,
            StrUtil.format("更新库存同步配置，{}", simpleDiffLog),
            null);
      }
    }

    // 处理店铺列表自身的增改
    updateShopListAndLog(cmd, existingShopMap);

    // --- 事务提交后才发送消息 ---
    if (isFirstAdd || allocUpdate) {
      scheduleNextMsgAfterCommit(cmd.getNextAllocTime(), ShopExecutionSignal.STOCK_ALLOC);
    }
    if (isFirstAdd || syncUpdate) {
      scheduleNextMsgAfterCommit(cmd.getNextSyncTime(), ShopExecutionSignal.STOCK_SYNC);
    }
  }

  private void updateShopListAndLog(
      InventoryAllocShopCmd cmd, Map<Long, InventoryAllocShop> existingShopMap) {
    final List<Long> shopIdList =
        cmd.getDtoList().stream()
            .map(InventoryAllocShopCmd.InventoryAllocShopDto::getShopId)
            .collect(Collectors.toList());
    final Map<Long, String> shopNameMap =
        shopService.lambdaQuery().in(Shop::getId, shopIdList).list().stream()
            .collect(Collectors.toMap(Shop::getId, Shop::getName));

    // 找出新增的店铺
    final List<InventoryAllocShopCmd.InventoryAllocShopDto> addDtoList =
        cmd.getDtoList().stream().filter(val -> val.getId() == null).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(addDtoList)) {
      final List<InventoryAllocShop> addEntityList = buildEntity(cmd, addDtoList);
      final String addedShopNames =
          addEntityList.stream()
              .map(val -> shopNameMap.get(val.getShopId()))
              .collect(Collectors.joining("，"));
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          INVENTORY_ALLOC_SHOP,
          0L,
          StrUtil.format("新增库存同步店铺，{}", addedShopNames),
          null);
      inventoryAllocShopService.saveBatch(addEntityList);
    }

    // 找出更新的店铺
    final List<InventoryAllocShopCmd.InventoryAllocShopDto> updateDtoList =
        cmd.getDtoList().stream().filter(val -> val.getId() != null).collect(Collectors.toList());
    if (CollUtil.isNotEmpty(updateDtoList)) {
      List<InventoryAllocShop> updateEntityList = buildEntity(cmd, updateDtoList);
      addUpdateLog(updateDtoList, existingShopMap, shopNameMap);
      inventoryAllocShopService.updateBatchById(updateEntityList);
    }
  }

  /** 将消息发送注册到事务同步器，在事务成功提交后执行。 */
  private void scheduleNextMsgAfterCommit(Long nextTime, ShopExecutionSignal signal) {
    TransactionSynchronizationManager.registerSynchronization(
        new TransactionSynchronizationAdapter() {
          @Override
          public void afterCommit() {
            sendNextMsg(nextTime, signal);
          }
        });
  }

  // 实际的消息发送逻辑
  private void sendNextMsg(Long nextTime, ShopExecutionSignal signal) {
    // 时间戳转换为毫秒
    long triggerTime = nextTime * 1000;
    final ShopExecutionDto param = new ShopExecutionDto(signal);
    final DelayMsgBizDto dto =
        DelayMsgBizDto.builder()
            .properties(
                new DelayMsgBizDto.PropertiesDTO(triggerTime, SpringUtil.getApplicationName()))
            .topic(shopStockTopic)
            .keys(RandomUtil.randomNumbers(12))
            .body(
                new String(
                    Base64.getEncoder().encode(JsonUtil.toJson(param).getBytes()),
                    StandardCharsets.UTF_8))
            .build();
    try {
      rocketMQProducer.syncSend(dto, delayTopic);
      log.info(
          "成功调度延时消息, Topic: {}, Signal: {}, 预定触发时间: {}", delayTopic, signal, new Date(triggerTime));
    } catch (Exception e) {
      log.error("发送延时消息失败, Topic: {}, Signal: {}", delayTopic, signal, e);
    }
  }

  private List<InventoryAllocShop> buildEntity(
      InventoryAllocShopCmd cmd, List<InventoryAllocShopCmd.InventoryAllocShopDto> list) {
    return list.stream()
        .map(
            dto -> {
              InventoryAllocShop shop = new InventoryAllocShop();
              shop.setId(dto.getId());
              shop.setShopId(dto.getShopId());
              shop.setInventoryWeight(dto.getInventoryWeight());
              shop.setStatus(dto.getStatus());
              shop.setWarnThreshold(dto.getWarnThreshold());
              // 关键：为每条记录都设置上全局的调度配置
              shop.setNextAllocTime(cmd.getNextAllocTime());
              shop.setAllocInterval(cmd.getAllocInterval());
              shop.setNextSyncTime(cmd.getNextSyncTime());
              shop.setSyncInterval(cmd.getSyncInterval());
              return shop;
            })
        .collect(Collectors.toList());
  }

  private void addUpdateLog(
      List<InventoryAllocShopCmd.InventoryAllocShopDto> uList,
      Map<Long, InventoryAllocShop> oldShopMap,
      Map<Long, String> shopNameMap) {
    final Map<Long, CompareListBo> oBoMap =
        oldShopMap.values().stream()
            .map(CompareListBo::fromEntity)
            .collect(Collectors.toMap(CompareListBo::getShopId, Function.identity()));

    final List<CompareListBo> nBoList =
        uList.stream().map(CompareListBo::fromCmdDto).collect(Collectors.toList());

    List<String> logs = new LinkedList<>();
    DiffUtil.ObjListDiff objListDiff =
        DiffUtil.diffObjList(new ArrayList<>(oBoMap.values()), nBoList, CompareListBo.class, true);

    objListDiff
        .getChangeMap()
        .forEach(
            (shopId, changes) -> {
              final String shopName = shopNameMap.get((Long) shopId);
              final String changeDetails =
                  changes.stream()
                      .map(
                          change -> {
                            if (change.getProperty().equals("状态")) {
                              return StrUtil.format(
                                  "【{}】从 {} 改为 {}",
                                  change.getProperty(),
                                  IEnum.getEnumByValue(
                                          InventoryAllocShopStatus.class, change.getOldVal())
                                      .getDesc(),
                                  IEnum.getEnumByValue(
                                          InventoryAllocShopStatus.class, change.getNewVal())
                                      .getDesc());
                            } else {
                              return StrUtil.format(
                                  "【{}】从 {} 改为 {}",
                                  change.getProperty(),
                                  change.getOldVal(),
                                  change.getNewVal());
                            }
                          })
                      .collect(Collectors.joining("；"));
              logs.add(StrUtil.format("店铺：{}，{}", shopName, changeDetails));
            });

    if (!logs.isEmpty()) {
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          INVENTORY_ALLOC_SHOP,
          0L,
          StrUtil.format("更新库存同步店铺，{}", String.join("。 ", logs)),
          null);
    }
  }

  @Override
  public void nextHandler(ShopExecutionSignal signal) {
    final List<InventoryAllocShop> list = inventoryAllocShopService.lambdaQuery().list();
    if (CollUtil.isEmpty(list)) {
      log.warn("调度任务触发，但无任何店铺配置，任务终止。 Signal: {}", signal);
      return;
    }

    final InventoryAllocShop firstShop = list.get(0); // 基准配置
    final List<Long> shopIdList =
        list.stream().map(InventoryAllocShop::getShopId).distinct().collect(Collectors.toList());
    final Map<Long, String> shopMap =
        shopService.lambdaQuery().in(Shop::getId, shopIdList).list().stream()
            .collect(Collectors.toMap(Shop::getId, Shop::getName));
    String shopNameStr = String.join("，", shopMap.values());

    if (ShopExecutionSignal.STOCK_ALLOC.equals(signal)) {
      try {
        allocHandler(list);
        operateLogGateway.addOperatorLog(
            -1L,
            INVENTORY_ALLOC_SHOP,
            0L,
            StrUtil.format("系统触发库存自动分配任务，店铺列表：{}", shopNameStr),
            null);
      } catch (Exception e) {
        log.error("系统触发库存自动分配任务异常", e);
        operateLogGateway.addOperatorLog(
            -1L,
            INVENTORY_ALLOC_SHOP,
            0L,
            StrUtil.format("系统触发库存自动分配任务失败，店铺列表：{}", shopNameStr),
            null);
      }

      Long nextAllocTime = firstShop.getNextAllocTime() + (3600L * firstShop.getAllocInterval());
      for (InventoryAllocShop val : list) {
        val.setLastAllocTime(val.getNextAllocTime());
        val.setNextAllocTime(nextAllocTime);
      }
      inventoryAllocShopService.updateBatchById(list);

      sendNextMsg(nextAllocTime, signal);
    }

    if (ShopExecutionSignal.STOCK_SYNC.equals(signal)) {
      try {
        syncHandler(list);
        operateLogGateway.addOperatorLog(
            -1L,
            INVENTORY_ALLOC_SHOP,
            0L,
            StrUtil.format("系统触发库存同步到平台任务，店铺列表：{}", shopNameStr),
            null);
      } catch (Exception e) {
        log.error("系统触发库存同步到平台任务异常", e);
        operateLogGateway.addOperatorLog(
            -1L,
            INVENTORY_ALLOC_SHOP,
            0L,
            StrUtil.format("系统触发库存同步到平台任务失败，店铺列表：{}", shopNameStr),
            null);
      }

      Long nextSyncTime = firstShop.getNextSyncTime() + (3600L * firstShop.getSyncInterval());
      for (InventoryAllocShop val : list) {
        val.setLastSyncTime(val.getNextSyncTime());
        val.setNextSyncTime(nextSyncTime);
      }
      inventoryAllocShopService.updateBatchById(list);

      sendNextMsg(nextSyncTime, signal);
    }
  }

  // --- 内部比较类 DTOs, ---
  @Data
  private static class CompareListBo {
    @Id private Long shopId;

    @PropertyName("库存占比")
    private Integer inventoryWeight;

    @PropertyName("状态")
    private InventoryAllocShopStatus status;

    @PropertyName("低库存预警")
    private Integer warnThreshold;

    public static CompareListBo fromEntity(InventoryAllocShop entity) {
      CompareListBo bo = new CompareListBo();
      bo.setShopId(entity.getShopId());
      bo.setInventoryWeight(entity.getInventoryWeight());
      bo.setStatus(entity.getStatus());
      bo.setWarnThreshold(entity.getWarnThreshold());
      return bo;
    }

    public static CompareListBo fromCmdDto(InventoryAllocShopCmd.InventoryAllocShopDto dto) {
      CompareListBo bo = new CompareListBo();
      bo.setShopId(dto.getShopId());
      bo.setInventoryWeight(dto.getInventoryWeight());
      bo.setStatus(dto.getStatus());
      bo.setWarnThreshold(dto.getWarnThreshold());
      return bo;
    }
  }

  @Data
  private static class CompareSettingBo {
    @Id private Long id = 0L;

    @PropertyName("下次库存自动分配时间")
    private Long nextAllocTime;

    @PropertyName("库存分配周期")
    private Integer allocInterval;

    @PropertyName("下次库存同步时间")
    private Long nextSyncTime;

    @PropertyName("'库存同步周期'")
    private Integer syncInterval;

    public static CompareSettingBo fromEntity(InventoryAllocShop entity) {
      CompareSettingBo bo = new CompareSettingBo();
      bo.setNextAllocTime(entity.getNextAllocTime());
      bo.setAllocInterval(entity.getAllocInterval());
      bo.setNextSyncTime(entity.getNextSyncTime());
      bo.setSyncInterval(entity.getSyncInterval());
      return bo;
    }

    public static CompareSettingBo fromCmd(InventoryAllocShopCmd cmd) {
      CompareSettingBo bo = new CompareSettingBo();
      bo.setNextAllocTime(cmd.getNextAllocTime());
      bo.setAllocInterval(cmd.getAllocInterval());
      bo.setNextSyncTime(cmd.getNextSyncTime());
      bo.setSyncInterval(cmd.getSyncInterval());
      return bo;
    }
  }

  @Override
  public void allocHandler(List<InventoryAllocShop> list) {}

  @Override
  public void syncHandler(List<InventoryAllocShop> list) {}
}
