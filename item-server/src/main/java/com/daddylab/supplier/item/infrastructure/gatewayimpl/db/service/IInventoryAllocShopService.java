package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;

/**
 * <p>
 * 库存分配店铺设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface IInventoryAllocShopService extends IDaddyService<InventoryAllocShop> {

}
