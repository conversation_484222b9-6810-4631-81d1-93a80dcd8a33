package com.daddylab.supplier.item.domain.item.gateway;

import com.alibaba.cola.dto.PageResponse;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ItemSkuSpecVo;
import com.daddylab.supplier.item.controller.item.dto.ItemSpecPageQuery;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerPersonCmd;
import com.daddylab.supplier.item.domain.purchase.dto.PurchaseItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemExpressTemplate;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemOperator;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemPrice;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSkuAttrRef;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemImageType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerPersonResp;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 10:48 上午
 * @description
 */
public interface ItemGateway {

    /**
     * 根据商品id，查询转换 商品基础信息实体。
     */
    ItemBaseDO getBaseInfo(Long itemId);

    /**
     * 查询商品基础信息（带主图）
     */
    ItemBaseDO getBaseInfoWithImage(Long itemId);

    /**
     * 根据商品id，批量查询商品基础信息（带主图）
     */
    List<ItemBaseDO> getBaseInfoWithImage(List<Long> itemIds);

    /**
     * 获取商品
     */
    Item getItem(Long itemId);

    /**
     * 获取商品
     */
    Item getItem(String itemNo);

    Long getProviderIdBySkuCode(String skuCode);

    /**
     * 获取商品
     */
    List<Item> getItemBatchByIds(List<Long> itemIds);

    /**
     * 获取商品
     */
    List<Item> getItemBatchByCodes(List<String> itemCodes);

    /**
     * 根据P系统款号获取商品
     * @param psysCodes P系统款号
     */
    List<Item> getItemBatchByPsysCode(List<String> psysCodes);


    Map<Long, String> getItemNameMap(List<Long> idList);

    /**
     * 保存item
     */
    Long saveItem(Item item);

    Optional<String> getLatestItemCode();

    /**
     * 商品关联的供应商款号是否重复，一个商品只
     */
    Boolean isRepeatPartnerProviderItemSn(String partnerProviderItemSn);

    /**
     * 判断此 item是否重复。 categoryId,brandId,providerId,name,构成唯一id.商品名称+品牌+品类+供应商。确定商品唯一。
     */
    Boolean isRepeat(String itemName);


    /**
     * 查询商品价格信息
     */
    List<ItemPrice> getPriceList(Long itemId);

    /**
     * 获取此关联此商品的子商品
     *
     * @param itemId
     * @return
     */
    List<Item> getSonItem(Long itemId);

    /**
     * 批量保存或者更新价格
     */
    void saveOrUpdateBatchPrice(List<ItemPrice> list);

    /**
     * 删除所有和itemPriceIds无关的价格信息
     */
    void removePriceWithIds(List<Long> keepItemPriceIds);

    /**
     * 查询 item下所有sku
     */
    List<ItemSku> getSkuList(Long itemId);

    void removeSku(List<Long> skuIdList);

    Optional<String> getLatestSkuCode(Long itemId);

    /**
     * 根据skuId查询 sku和属性的关联关系
     */
    List<SkuAttrRefDO> getSkuAttrList(List<Long> skuIdList);

    /**
     * 保存itemSku
     */
    Long saveItemSkuReturnId(ItemSku itemSku);

    /**
     * 批量插入或者更新item_sku
     */
    void saveOrUpdateBatchItemSku(List<ItemSku> list);

    /**
     * 根据skuId获取商品sku
     *
     * @param id
     * @return
     */
    Optional<ItemSku> getByItemSkuId(Long id);

    /**
     * 根据商品id获取sku数量
     *
     * @param itemId
     * @return
     */
    Integer getSkuCount(Long itemId);

    /**
     * 查询 商品 图片
     */
    List<ItemImage> getImageListByType(Long itemId, ItemImageType itemImageType);

    /**
     * 获取商品图片，仅返回链接
     * @param itemId 商品id
     * @param itemImageType 图片类型
     * @return 图片链接
     */
    List<String> getImageUrlList(Long itemId, ItemImageType itemImageType);

    /**
     * 移除 item_image
     */
    void removeImages(List<Long> itemImageIds);

    /**
     * 批量保存或者更新图片
     */
    void saveOrUpdateBatchImage(List<ItemImage> list);

    /**
     * 获取商品运营信息
     */
    List<ItemOperator> getItemOperation(Long itemId);

    /**
     * 插入更新运营人员
     */
    void saveOrUpdateOperation(ItemOperator itemOperator);

    /**
     * 批量保存运营人员信息
     */
    void saveOrUpdateBatchOperation(List<ItemOperator> itemOperator);

    /**
     * 查询 商品仓信息
     */
    ItemProcurement getProcurementByItemId(Long itemId);

    /**
     * 保存快递模板
     */
    void saveOrUpdateBatchExpress(List<ItemExpressTemplate> list);

    /**
     * 根据商品id查询快递模板id集合
     */
    List<ItemExpressTemplate> getExpressByItemId(Long itemId);

    /**
     * 根据商品id查询快递模板id集合
     */
    ItemExpressTemplate getExpressTemplateByItemId(Long itemId);

    /**
     * 保存商品仓库信息
     *
     * @param itemProcurement db item_procurement
     */
    void saveOrUpdateProcurement(ItemProcurement itemProcurement);

    /**
     * 更新 sku属性值
     *
     * @param itemAttrDto
     */
    void updateSkuAttr(ItemAttrDto itemAttrDto);

    /**
     * 保存skuAttr,返回表sku_attr id
     */
    Long saveItemAttrReturnId(ItemAttr itemAttr);

    /**
     * 批量保存 sku_attr_ref
     */
    void saveBatchItemSkuAttrRef(List<ItemSkuAttrRef> list);


    /**
     * 查询 合作伙伴系统的 商品信息
     */
    List<PartnerItemResp> partnerQuery(PartnerItemCmd cmd);

    Optional<PartnerItemResp> partnerQueryByCode(String code);

    /**
     * 是否存在品牌关联商品
     */
    boolean existBrandAssociativeItem(Long brandId);

    /**
     * 获取采购员信息
     *
     * @param userId
     * @param userName
     * @return
     */
    Long saveOrUpdateBuyer(Long userId, String userName);

    /**
     * 保存sku的金蝶id
     *
     * @param skuId
     * @param kingDeeId
     */
    void fillingSkuKingDeeId(Long skuId, String kingDeeId);


    /**
     * 查询商品的运营人员id list
     *
     * @param itemId
     * @return
     */
    List<Long> getRunnerIdList(Long itemId);

    /**
     * 移除 运营人员id
     *
     * @param runnerId
     */
    void removeRunner(List<Long> runnerId);


    Boolean isSkuCodeRepeat(String skuCode);

    /**
     * 判断商品特殊编码是否重复
     *
     * @param specialCode
     * @return
     */
    Boolean isSpecialSkuCodeRepeat(String specialCode);

    Boolean isSkuBarCodeRepeat(String barCode);

    /**
     * 判断sku特殊编码是否重复
     *
     * @param specialCode
     * @return
     */
    Boolean isSpecialItemCodeRepeat(String specialCode);

    /**
     * 移除快递模板
     *
     * @param idList
     */
    void removeExpress(List<Long> idList);

    /**
     * 根据sku查询商品相关信息
     *
     * @param itemSku
     * @return
     */
    PurchaseItem getPurchaseBySku(String itemSku);

    /**
     * 根据sku查询商品相关信息
     *
     * @param id
     * @return
     */
    NewGoodsVo getNewGoodsById(Long id);

    /**
     * 标记商品已同步到旺店通
     *
     * @param itemId 商品ID
     */
    void setIsWdtSynced(Long itemId);

    /**
     * 查询 合作伙伴系统的 员工信息
     * @param cmd
     * @return
     */
    List<PartnerPersonResp> partnerPersonQuery(PartnerPersonCmd cmd);

    /**
     * 或者sku规格列表
     * @param query
     * @return
     */
    PageResponse<ItemSkuSpecVo> getListBySpec(ItemSpecPageQuery query);

    String getItemCodeByItemId(Long itemId);

    List<Item> getItemList();

    boolean setWarehouse(Long itemId, String warehouseNo);

    Integer getBusinessLine(Long itemId);
    
    Map<String,Item> queryItemBatchBySkuCodes(Collection<String> skuCodes);
}

