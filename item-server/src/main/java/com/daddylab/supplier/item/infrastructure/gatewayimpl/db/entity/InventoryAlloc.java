package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 库存分配
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InventoryAlloc implements Serializable {

    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Long createdAt;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private Long createdUid;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedAt;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updatedUid;

    /** 是否已删除 */
    @TableLogic(value = "0", delval = "id")
    private Long isDel;

    /** 删除时间 */
    @TableField(fill = FieldFill.DEFAULT)
    private Long deletedAt;

    /** 平台商品ID */
    private Long platformItemId;

    /** 平台商品SkuID */
    private Long platformItemSkuId;

    /** SKU编码 */
    private String skuCode;

    /** 总库存数 */
    private Integer totalInventoryNum;

    /** 系统计算出的分配数量 */
    private Integer calcNum;

    /** 最终生效的分配数量（允许人工调整） */
    private Integer effectiveNum;

    /** 低库存预警 */
    private Integer warnThreshold;

    /** 是否开启库存同步 */
    private Boolean syncEnabled;

    /** 是否开启库存锁定 */
    private Boolean lockEnabled;

    /** 最后分配时间 */
    private Long lastAllocTime;

    /** 最后同步时间 */
    private Long lastSyncTime;

    /** 最后同步库存 */
    private Integer lastSyncNum;

    /**
     * 是否是组合装 0：非组合装 1：组合装单品 2：组合装
     */
    private Integer isSuite;

    /**
     * 计算过程
     */
    private String calculation;
}
